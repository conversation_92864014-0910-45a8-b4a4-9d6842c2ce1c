import { createAuthClient } from "better-auth/react"
import type { Session, User } from "./auth-shared"

export const authClient = createAuthClient({
  baseURL: "https://nxtdotx.co.za",
  basePath: "/api/auth", // Better-Auth API endpoint
})

export type { Session, User }

// Re-export auth utilities
export {
  getUserPermissions,
  hasPermission,
  hasAnyPermission,
  isRole,
  isRoleOrHigher
} from "./auth-shared"