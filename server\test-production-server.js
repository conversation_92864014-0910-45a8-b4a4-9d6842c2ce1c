import express from "express";
import cors from "cors";
import { createServer } from "http";
import dotenv from "dotenv";

// Load environment variables
dotenv.config();

const app = express();

// CORS configuration for production
app.use(cors({
  origin: [
    'https://nxtdotx.co.za',
    'https://www.nxtdotx.co.za',
    process.env.PRODUCTION_URL || 'https://nxtdotx.co.za'
  ],
  credentials: true
}));

app.use(express.json());

// Health check endpoint
app.get("/health", (req, res) => {
  res.status(200).json({
    status: "healthy",
    timestamp: new Date().toISOString(),
    service: "nxt-production-server",
    environment: process.env.NODE_ENV || 'production',
    version: process.env.VITE_APP_VERSION || '3.5.1'
  });
});

// API status endpoint
app.get("/api/status", (req, res) => {
  res.json({
    status: "operational",
    services: {
      database: process.env.DATABASE_URL ? "configured" : "missing",
      auth: process.env.BETTER_AUTH_SECRET ? "ready" : "not configured",
      storage: process.env.VITE_SUPABASE_URL ? "available" : "not configured"
    },
    environment: {
      node_env: process.env.NODE_ENV || 'production',
      production_url: process.env.PRODUCTION_URL || 'not set',
      supabase_configured: !!process.env.VITE_SUPABASE_URL,
      database_configured: !!process.env.DATABASE_URL,
      auth_configured: !!process.env.BETTER_AUTH_SECRET
    },
    config_check: {
      supabase_url: process.env.VITE_SUPABASE_URL ? '✅ Set' : '❌ Missing',
      database_url: process.env.DATABASE_URL ? '✅ Set' : '❌ Missing',
      auth_secret: process.env.BETTER_AUTH_SECRET ? '✅ Set' : '❌ Missing',
      production_url: process.env.PRODUCTION_URL ? '✅ Set' : '❌ Missing'
    }
  });
});

// Serve static files (built frontend)
app.use(express.static('dist'));

// Catch-all handler for SPA routing
app.get('/', (req, res) => {
  res.sendFile('index.html', { root: 'dist' });
});

const PORT = process.env.PORT || 3000;
const HOST = process.env.HOST || '0.0.0.0';

const server = createServer(app);

server.listen(PORT, HOST, () => {
  console.log(`🚀 NXT Production Server running on ${HOST}:${PORT}`);
  console.log(`🌐 Environment: ${process.env.NODE_ENV || 'production'}`);
  console.log(`🔗 Health check: http://${HOST}:${PORT}/health`);
  console.log(`📊 Status: http://${HOST}:${PORT}/api/status`);
  console.log(`🎯 Ready for production deployment!`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');
  server.close(() => {
    console.log('Process terminated');
  });
});
