# deploy-docker.ps1
# PowerShell script to build and push Docker image to GitHub Container Registry (GHCR)

# Allow GHCR authentication and push
$SkipPush = $false

$Image = "ghcr.io/nxtleveltech1/nxtdotx:latest"
$User = "nxtleveltech1"

Write-Host "Building Docker image locally"
docker build -t $Image -f Dockerfile .

if (-not $SkipPush) {
    # Only attempt to push if authentication is set up
    if ($env:GHCR_TOKEN) {
        Write-Host "Logging in to ghcr.io as $User"
        docker login ghcr.io -u $User -p $env:GHCR_TOKEN

        Write-Host "Pushing Docker image $Image"
        docker push $Image
    } else {
        Write-Host "Skipping push to GHCR (no token provided)"
    }
}

Write-Host "Running the image locally"
docker run -d -p 3000:3000 $Image

Write-Host "Deployment complete: $Image"