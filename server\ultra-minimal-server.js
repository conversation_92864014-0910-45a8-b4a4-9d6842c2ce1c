/**
 * ULTRA MINIMAL SERVER - NO EXPRESS, NO PROBLEMATIC DEPENDENCIES
 * Pure Node.js HTTP server that just serves the frontend
 */

import http from 'http';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables
dotenv.config();

console.log('🚀 Starting ULTRA MINIMAL Server (Pure Node.js)...');
console.log('🌐 Environment:', process.env.NODE_ENV || 'production');

const PORT = process.env.PORT || 3000;
const DIST_PATH = path.join(path.dirname(__dirname), 'dist');

// MIME types for static files
const mimeTypes = {
  '.html': 'text/html',
  '.js': 'text/javascript',
  '.css': 'text/css',
  '.json': 'application/json',
  '.png': 'image/png',
  '.jpg': 'image/jpg',
  '.gif': 'image/gif',
  '.ico': 'image/x-icon',
  '.svg': 'image/svg+xml',
  '.woff': 'font/woff',
  '.woff2': 'font/woff2',
  '.ttf': 'font/ttf',
  '.eot': 'application/vnd.ms-fontobject'
};

// Create HTTP server
const server = http.createServer((req, res) => {
  const url = req.url;
  const method = req.method;

  console.log(`${method} ${url}`);

  // Health check endpoint
  if (url === '/health') {
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({
      status: "healthy",
      timestamp: new Date().toISOString(),
      server: "ultra-minimal-nodejs",
      message: "Pure Node.js server running without problematic dependencies"
    }));
    return;
  }

  // Mock auth endpoints
  if (url.startsWith('/api/auth/')) {
    res.writeHead(501, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({
      error: "Authentication not implemented",
      message: "Better Auth server temporarily disabled due to dependency issues",
      endpoint: url,
      method: method
    }));
    return;
  }

  // Serve static files
  let filePath = path.join(DIST_PATH, url === '/' ? 'index.html' : url);

  // Security check - prevent directory traversal
  if (!filePath.startsWith(DIST_PATH)) {
    res.writeHead(403, { 'Content-Type': 'text/plain' });
    res.end('Forbidden');
    return;
  }

  // Check if file exists
  fs.access(filePath, fs.constants.F_OK, (err) => {
    if (err) {
      // File not found, serve index.html for SPA routing
      filePath = path.join(DIST_PATH, 'index.html');
    }

    // Read and serve the file
    fs.readFile(filePath, (err, content) => {
      if (err) {
        res.writeHead(500, { 'Content-Type': 'text/plain' });
        res.end('Internal Server Error');
        return;
      }

      // Get MIME type
      const ext = path.extname(filePath);
      const mimeType = mimeTypes[ext] || 'application/octet-stream';

      res.writeHead(200, { 'Content-Type': mimeType });
      res.end(content);
    });
  });
});

// Global error handlers
process.on('uncaughtException', (error) => {
  console.error('❌ Uncaught Exception:', error);
  console.error('🔍 Stack:', error.stack);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
});

// Start server
server.listen(PORT, () => {
  console.log(`✅ ULTRA MINIMAL Server running on port ${PORT}`);
  console.log(`🌐 Frontend: http://localhost:${PORT}`);
  console.log(`🔍 Health: http://localhost:${PORT}/health`);
  console.log(`⚠️  Auth endpoints disabled (501 responses)`);
  console.log(`🎯 Pure Node.js server - no Express, no problematic dependencies`);
});

server.on('error', (error) => {
  console.error('❌ Server error:', error);
});
