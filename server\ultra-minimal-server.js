/**
 * ULTRA MINIMAL SERVER - NO EXPRESS, NO PROBLEMATIC DEPENDENCIES
 * Pure Node.js HTTP server that just serves the frontend
 */

import http from 'http';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables
dotenv.config();

console.log('🚀 Starting ULTRA MINIMAL Server (Pure Node.js)...');
console.log('🌐 Environment:', process.env.NODE_ENV || 'production');

const PORT = process.env.PORT || 3000;
const DIST_PATH = path.join(path.dirname(__dirname), 'dist');

// MIME types for static files
const mimeTypes = {
  '.html': 'text/html',
  '.js': 'text/javascript',
  '.css': 'text/css',
  '.json': 'application/json',
  '.png': 'image/png',
  '.jpg': 'image/jpg',
  '.gif': 'image/gif',
  '.ico': 'image/x-icon',
  '.svg': 'image/svg+xml',
  '.woff': 'font/woff',
  '.woff2': 'font/woff2',
  '.ttf': 'font/ttf',
  '.eot': 'application/vnd.ms-fontobject'
};

// Create HTTP server
const server = http.createServer((req, res) => {
  const url = req.url;
  const method = req.method;

  console.log(`${method} ${url}`);

  // Health check endpoint
  if (url === '/health') {
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({
      status: "healthy",
      timestamp: new Date().toISOString(),
      server: "ultra-minimal-nodejs",
      message: "Pure Node.js server running without problematic dependencies"
    }));
    return;
  }

  // Mock auth endpoints - temporary development bypass
  if (url.startsWith('/api/auth/')) {
    // Handle CORS preflight
    if (method === 'OPTIONS') {
      res.writeHead(200, {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      });
      res.end();
      return;
    }

    // Mock session endpoint
    if (url === '/api/auth/get-session') {
      res.writeHead(200, {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      });
      res.end(JSON.stringify({
        data: {
          session: {
            id: 'dev-session-123',
            userId: 'dev-user-123',
            expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
            token: 'dev-token-123'
          },
          user: {
            id: 'dev-user-123',
            name: 'Development User',
            email: '<EMAIL>',
            emailVerified: true,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            role: 'admin',
            permissions: JSON.stringify(['VIEW_DASHBOARD', 'MANAGE_USERS', 'ADMIN_ACCESS']),
            firstName: 'Dev',
            lastName: 'User',
            displayName: 'Development User',
            department: 'Development',
            isActive: true
          }
        },
        error: null
      }));
      return;
    }

    // Mock sign-in endpoint
    if (url === '/api/auth/sign-in/email' && method === 'POST') {
      res.writeHead(200, {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      });
      res.end(JSON.stringify({
        data: {
          session: {
            id: 'dev-session-123',
            userId: 'dev-user-123',
            expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
            token: 'dev-token-123'
          },
          user: {
            id: 'dev-user-123',
            name: 'Development User',
            email: '<EMAIL>',
            emailVerified: true,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            role: 'admin',
            permissions: JSON.stringify(['VIEW_DASHBOARD', 'MANAGE_USERS', 'ADMIN_ACCESS']),
            firstName: 'Dev',
            lastName: 'User',
            displayName: 'Development User',
            department: 'Development',
            isActive: true
          }
        },
        error: null
      }));
      return;
    }

    // Mock other auth endpoints
    res.writeHead(200, {
      'Content-Type': 'application/json',
      'Access-Control-Allow-Origin': '*'
    });
    res.end(JSON.stringify({
      data: null,
      error: { message: `Mock auth endpoint: ${url}` }
    }));
    return;
  }

  // Serve static files
  let filePath = path.join(DIST_PATH, url === '/' ? 'index.html' : url);

  // Security check - prevent directory traversal
  if (!filePath.startsWith(DIST_PATH)) {
    res.writeHead(403, { 'Content-Type': 'text/plain' });
    res.end('Forbidden');
    return;
  }

  // Check if file exists
  fs.access(filePath, fs.constants.F_OK, (err) => {
    if (err) {
      // File not found, serve index.html for SPA routing
      filePath = path.join(DIST_PATH, 'index.html');
    }

    // Read and serve the file
    fs.readFile(filePath, (err, content) => {
      if (err) {
        res.writeHead(500, { 'Content-Type': 'text/plain' });
        res.end('Internal Server Error');
        return;
      }

      // Get MIME type
      const ext = path.extname(filePath);
      const mimeType = mimeTypes[ext] || 'application/octet-stream';

      res.writeHead(200, { 'Content-Type': mimeType });
      res.end(content);
    });
  });
});

// Global error handlers
process.on('uncaughtException', (error) => {
  console.error('❌ Uncaught Exception:', error);
  console.error('🔍 Stack:', error.stack);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
});

// Start server
server.listen(PORT, () => {
  console.log(`✅ ULTRA MINIMAL Server running on port ${PORT}`);
  console.log(`🌐 Frontend: http://localhost:${PORT}`);
  console.log(`🔍 Health: http://localhost:${PORT}/health`);
  console.log(`⚠️  Auth endpoints disabled (501 responses)`);
  console.log(`🎯 Pure Node.js server - no Express, no problematic dependencies`);
});

server.on('error', (error) => {
  console.error('❌ Server error:', error);
});
