#!/usr/bin/env bash
set -euo pipefail

# =============================================================================
# Create Production Deployment Package
# =============================================================================

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# Configuration
PACKAGE_NAME="nxtdotx-production-$(date +%Y%m%d-%H%M%S)"
PACKAGE_DIR="./deployment-packages/$PACKAGE_NAME"

log_info "Creating production deployment package..."
log_info "Package: $PACKAGE_NAME"

# Create package directory
mkdir -p "$PACKAGE_DIR"

# Build the application
log_info "Building application..."
npm run build

# Copy necessary files
log_info "Copying files..."
cp -r dist "$PACKAGE_DIR/"
cp -r server "$PACKAGE_DIR/"
cp package.json "$PACKAGE_DIR/"
cp package-lock.json "$PACKAGE_DIR/"
cp Dockerfile "$PACKAGE_DIR/"
cp .env.example "$PACKAGE_DIR/"

# Create deployment instructions
cat > "$PACKAGE_DIR/DEPLOYMENT_INSTRUCTIONS.md" << 'EOF'
# Production Deployment Instructions

## Quick Start

1. **Copy your environment file**:
   ```bash
   cp .env.example .env
   # Edit .env with your production values
   ```

2. **Deploy with Docker**:
   ```bash
   docker build -t nxtdotx:latest .
   docker run -d -p 80:3000 --env-file .env --name nxtdotx nxtdotx:latest
   ```

3. **Test deployment**:
   ```bash
   curl http://your-server/health
   ```

## Alternative: Node.js Deployment

1. **Install dependencies**:
   ```bash
   npm ci --only=production
   ```

2. **Start the server**:
   ```bash
   NODE_ENV=production node server/production-better-auth-server.js
   ```

## Health Check
- Endpoint: `/health`
- Status: `/api/status`

## Environment Variables Required
See `.env.example` for all required variables.
EOF

# Create start script
cat > "$PACKAGE_DIR/start-production.sh" << 'EOF'
#!/bin/bash
set -e

echo "🚀 Starting NXT-DOT-X Production Server..."

# Check if .env exists
if [ ! -f .env ]; then
    echo "❌ .env file not found. Copy .env.example to .env and configure it."
    exit 1
fi

# Install dependencies if needed
if [ ! -d node_modules ]; then
    echo "📦 Installing dependencies..."
    npm ci --only=production
fi

# Start the server
echo "🌐 Starting server..."
NODE_ENV=production node server/production-better-auth-server.js
EOF

chmod +x "$PACKAGE_DIR/start-production.sh"

# Create archive
log_info "Creating deployment archive..."
cd deployment-packages
tar -czf "$PACKAGE_NAME.tar.gz" "$PACKAGE_NAME"
cd ..

log_success "Deployment package created successfully!"
log_info "Package location: ./deployment-packages/$PACKAGE_NAME.tar.gz"
log_info "Package size: $(du -h "./deployment-packages/$PACKAGE_NAME.tar.gz" | cut -f1)"

echo ""
echo "📦 Deployment Package Ready!"
echo "📁 Location: ./deployment-packages/$PACKAGE_NAME.tar.gz"
echo "📋 Instructions: See $PACKAGE_NAME/DEPLOYMENT_INSTRUCTIONS.md"
echo ""
echo "🚀 Upload this package to your server and follow the instructions!"
