-- =====================================================
-- PHASE 2: CREATE BETTER AUTH TABLES MANUALLY
-- =====================================================
-- This script creates Better Auth tables in the production database

-- Better Auth user table
CREATE TABLE IF NOT EXISTS "user" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "email" TEXT NOT NULL UNIQUE,
    "emailVerified" BOOLEAN NOT NULL DEFAULT false,
    "name" TEXT,
    "createdAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "image" TEXT
);

-- Better Auth session table
CREATE TABLE IF NOT EXISTS "session" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "expiresAt" TIMESTAMP NOT NULL,
    "token" TEXT NOT NULL UNIQUE,
    "createdAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "ipAddress" TEXT,
    "userAgent" TEXT,
    "userId" TEXT NOT NULL REFERENCES "user"("id") ON DELETE CASCADE
);

-- Better Auth account table (for social providers and password auth)
CREATE TABLE IF NOT EXISTS "account" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "accountId" TEXT NOT NULL,
    "providerId" TEXT NOT NULL,
    "userId" TEXT NOT NULL REFERENCES "user"("id") ON DELETE CASCADE,
    "accessToken" TEXT,
    "refreshToken" TEXT,
    "idToken" TEXT,
    "accessTokenExpiresAt" TIMESTAMP,
    "refreshTokenExpiresAt" TIMESTAMP,
    "scope" TEXT,
    "password" TEXT,
    "createdAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Better Auth verification table (for email verification, password reset)
CREATE TABLE IF NOT EXISTS "verification" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "identifier" TEXT NOT NULL,
    "value" TEXT NOT NULL,
    "expiresAt" TIMESTAMP NOT NULL,
    "createdAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create Performance Indexes
CREATE INDEX IF NOT EXISTS "session_userId_idx" ON "session"("userId");
CREATE INDEX IF NOT EXISTS "session_token_idx" ON "session"("token");
CREATE INDEX IF NOT EXISTS "account_userId_idx" ON "account"("userId");
CREATE INDEX IF NOT EXISTS "account_providerId_idx" ON "account"("providerId");
CREATE INDEX IF NOT EXISTS "verification_identifier_idx" ON "verification"("identifier");
CREATE INDEX IF NOT EXISTS "user_email_idx" ON "user"("email");

-- Grant permissions to authenticated users
GRANT SELECT, INSERT, UPDATE, DELETE ON "user" TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON "session" TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON "account" TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON "verification" TO authenticated;

-- Grant permissions to service role
GRANT ALL ON "user" TO service_role;
GRANT ALL ON "session" TO service_role;
GRANT ALL ON "account" TO service_role;
GRANT ALL ON "verification" TO service_role;

-- Verify tables were created
SELECT 
    'Better Auth Tables Created' as status,
    (SELECT COUNT(*) FROM information_schema.tables WHERE table_name = 'user') as user_table,
    (SELECT COUNT(*) FROM information_schema.tables WHERE table_name = 'session') as session_table,
    (SELECT COUNT(*) FROM information_schema.tables WHERE table_name = 'account') as account_table,
    (SELECT COUNT(*) FROM information_schema.tables WHERE table_name = 'verification') as verification_table;
