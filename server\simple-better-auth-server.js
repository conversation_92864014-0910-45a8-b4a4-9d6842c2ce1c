/**
 * PHASE 3: SIMPLE BETTER AUTH SERVER
 * A working Better Auth server without complex dependencies
 */

import { betterAuth } from "better-auth";
import cors from "cors";
import dotenv from "dotenv";
import express from "express";
import path from "path";
import { Pool } from "pg";

// Load environment variables
dotenv.config();

console.log('🚀 Starting SIMPLE Better-Auth Server...');
console.log('🌐 Environment:', process.env.NODE_ENV || 'production');

const PORT = process.env.PORT || 3000;

// Validate required environment variables
const requiredEnvVars = [
  'DATABASE_URL',
  'BETTER_AUTH_SECRET'
];

const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
if (missingVars.length > 0) {
  console.error('❌ Missing required environment variables:', missingVars.join(', '));
  process.exit(1);
}

console.log('✅ All required environment variables are set');
console.log('🔗 Connecting to production database...');

// Create PostgreSQL connection pool (simpler than <PERSON><PERSON><PERSON>)
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
  max: 20,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
});

// Test database connection
async function testConnection() {
  try {
    const client = await pool.connect();
    const result = await client.query('SELECT NOW() as current_time, version() as pg_version');
    console.log('✅ Database connected successfully');
    console.log('📅 Server time:', result.rows[0].current_time);
    console.log('🗄️  PostgreSQL version:', result.rows[0].pg_version.split(' ')[0]);
    client.release();
    return true;
  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
    return false;
  }
}

// Initialize Better Auth with simple PostgreSQL adapter
let auth;
async function initializeBetterAuth() {
  try {
    console.log('🔧 Initializing Better Auth with PostgreSQL adapter...');

    // Test database connection first
    const client = await pool.connect();
    await client.query('SELECT 1');
    client.release();
    console.log('✅ Database connection verified for Better Auth');

    auth = betterAuth({
      database: {
        provider: "postgresql",
        connection: pool,
      },
      emailAndPassword: {
        enabled: true,
        requireEmailVerification: false,
      },
      session: {
        expiresIn: 60 * 60 * 24 * 7, // 7 days
        updateAge: 60 * 60 * 24, // 24 hours
      },
      secret: process.env.BETTER_AUTH_SECRET,
      baseURL: 'https://nxtdotx.co.za',
      trustedOrigins: [
        'https://nxtdotx.co.za',
        'https://www.nxtdotx.co.za'
      ]
    });

    console.log('✅ Better Auth initialized successfully');
    return true;
  } catch (error) {
    console.error('❌ Better Auth initialization failed:', error);
    console.error('🔍 Error details:', error.message);
    return false;
  }
}

// Create Express app
const app = express();

// CORS configuration
app.use(cors({
  origin: [
    'https://nxtdotx.co.za',
    'https://www.nxtdotx.co.za',
    'http://localhost:3000',
    'http://localhost:5173'
  ],
  credentials: true
}));

app.use(express.json());
app.use(express.static('dist'));

// Health check endpoint
app.get("/health", (req, res) => {
  res.status(200).json({
    status: "healthy",
    timestamp: new Date().toISOString(),
    auth: auth ? "initialized" : "not initialized",
    database: "connected",
    server: "simple-better-auth"
  });
});

// Better Auth endpoints
app.all("/api/auth/*", async (req, res) => {
  try {
    if (!auth) {
      return res.status(503).json({
        error: "Service unavailable",
        details: "Better Auth not initialized"
      });
    }
    console.log(`📡 ${req.method} ${req.url}`);
    return await auth.handler(req, res);
  } catch (error) {
    console.error("❌ Auth handler error:", error);
    res.status(500).json({
      error: "Internal server error",
      details: error.message
    });
  }
});

// Test endpoint for authentication
app.get("/api/test-auth", async (req, res) => {
  try {
    if (!auth) {
      return res.status(503).json({ error: "Auth not initialized" });
    }
    
    // Test creating a user
    const testResult = await auth.api.signUpEmail({
      body: {
        email: `test_${Date.now()}@example.com`,
        password: 'TestPassword123!',
        name: 'Test User'
      }
    });
    
    // Clean up test user
    if (testResult) {
      const client = await pool.connect();
      await client.query('DELETE FROM "user" WHERE email LIKE $1', [`<EMAIL>`]);
      client.release();
    }
    
    res.json({
      status: "success",
      message: "Better Auth is working correctly",
      test: "user creation successful"
    });
  } catch (error) {
    res.status(500).json({
      error: "Auth test failed",
      details: error.message
    });
  }
});

// Serve frontend for all other routes
app.get('*', (req, res) => {
  res.sendFile(path.join(process.cwd(), 'dist/index.html'));
});

// Global error handlers
process.on('uncaughtException', (error) => {
  console.error('❌ Uncaught Exception:', error);
  console.error('🔍 Stack:', error.stack);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
});

// Start server after successful database connection and auth initialization
testConnection().then(async (dbSuccess) => {
  if (dbSuccess) {
    const authSuccess = await initializeBetterAuth();
    if (authSuccess) {
      const server = app.listen(PORT, () => {
        console.log(`✅ SIMPLE Better Auth Server running on port ${PORT}`);
        console.log(`🌐 Frontend: https://nxtdotx.co.za`);
        console.log(`🔐 Auth API: https://nxtdotx.co.za/api/auth`);
        console.log(`🔍 Health: https://nxtdotx.co.za/health`);
        console.log(`🧪 Test Auth: https://nxtdotx.co.za/api/test-auth`);
      });
      
      server.on('error', (error) => {
        console.error('❌ Server error:', error);
      });
    } else {
      console.error('❌ Server failed to start due to Better Auth initialization issues');
      process.exit(1);
    }
  } else {
    console.error('❌ Server failed to start due to database connection issues');
    process.exit(1);
  }
}).catch(error => {
  console.error('❌ Startup error:', error);
  process.exit(1);
});
