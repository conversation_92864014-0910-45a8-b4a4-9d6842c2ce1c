import express from "express";
import cors from "cors";

const app = express();

app.use(cors({
  origin: 'http://localhost:3000',
  credentials: true
}));
app.use(express.json());

// Health check endpoint
app.get("/health", (req, res) => {
  res.status(200).json({
    status: "healthy",
    timestamp: new Date().toISOString(),
    service: "test-server"
  });
});

// Test auth endpoints
app.use("/api/auth", (req, res) => {
  console.log(`📡 ${req.method} ${req.url}`);
  res.json({
    message: "Auth endpoint working",
    method: req.method,
    url: req.url,
    path: req.path
  });
});

const PORT = process.env.PORT || 3001;

app.listen(PORT, () => {
  console.log(`✅ Test Server running on port ${PORT}`);
  console.log(`🌐 Health check: http://localhost:${PORT}/health`);
  console.log(`🔐 Auth endpoint: http://localhost:${PORT}/api/auth`);
});
