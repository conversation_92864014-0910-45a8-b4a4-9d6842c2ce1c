# NXT-DOT-X Cloud Environment Configuration
# Copy this file to .env and fill in your actual values

# =============================================================================
# MCP SERVER API KEYS - Add your actual API keys here
# =============================================================================

# Brave Search API (https://brave.com/search/api/)
BRAVE_API_KEY=your_brave_api_key_here

# Tavily Search API (https://tavily.com/)
TAVILY_API_KEY=your_tavily_api_key_here

# FireCrawl API (https://firecrawl.dev/)
FIRECRAWL_API_KEY=your_firecrawl_api_key_here

# Upstash Redis (https://upstash.com/)
UPSTASH_REDIS_REST_URL=your_upstash_redis_url_here
UPSTASH_REDIS_REST_TOKEN=your_upstash_redis_token_here

# Notion API (https://developers.notion.com/)
NOTION_API_KEY=your_notion_api_key_here

# Supabase MCP Configuration
SUPABASE_ACCESS_TOKEN=your_supabase_access_token_here
SUPABASE_PROJECT_ID=your_supabase_project_id_here

# Neo4j Aura (https://neo4j.com/cloud/aura/)
NEO4J_URI=your_neo4j_uri_here
NEO4J_USERNAME=your_neo4j_username_here
NEO4J_PASSWORD=your_neo4j_password_here

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================

# Supabase Configuration
VITE_SUPABASE_URL=https://your-project-ref.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key-here
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key-here

# Database Configuration (for server-side operations)
DATABASE_URL=postgresql://postgres:<EMAIL>:5432/postgres
VITE_SUPABASE_DB_NAME=postgres

# Better Auth Configuration
BETTER_AUTH_SECRET=your-32-character-secret-key-here-make-it-random-and-secure
BETTER_AUTH_URL=http://localhost:3000
VITE_BETTER_AUTH_SECRET=your-32-character-secret-key-here-make-it-random-and-secure
VITE_BETTER_AUTH_URL=http://localhost:3000

# Email Configuration (for Better Auth)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
FROM_EMAIL=<EMAIL>

# OAuth Providers (Optional)
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GITHUB_CLIENT_ID=your-github-client-id
GITHUB_CLIENT_SECRET=your-github-client-secret

# Cloudflare Turnstile (Optional)
VITE_TURNSTILE_SITE_KEY=your-turnstile-site-key
TURNSTILE_SECRET_KEY=your-turnstile-secret-key

# Server Configuration
PORT=3000
HOST=0.0.0.0
NODE_ENV=production

# Application Configuration
VITE_APP_NAME=NXT-DOT-X
VITE_APP_VERSION=1.0.0
VITE_APP_ENV=development

# API Configuration
VITE_API_BASE_URL=https://your-project-ref.supabase.co/functions/v1
VITE_STORAGE_URL=https://your-project-ref.supabase.co/storage/v1

# Authentication
VITE_AUTH_REDIRECT_URL=http://localhost:3000/auth/callback
VITE_AUTH_SITE_URL=http://localhost:3000

# Feature Flags
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_REALTIME=true
VITE_ENABLE_STORAGE=true

# Development/Debug
VITE_DEBUG_MODE=false
VITE_LOG_LEVEL=info

# Third-party Integrations
VITE_OPENAI_API_KEY=your-openai-key-here
VITE_STRIPE_PUBLISHABLE_KEY=your-stripe-key-here

# File Upload Limits
VITE_MAX_FILE_SIZE=10485760
VITE_ALLOWED_FILE_TYPES=pdf,doc,docx,xls,xlsx,csv,txt,jpg,jpeg,png,gif 