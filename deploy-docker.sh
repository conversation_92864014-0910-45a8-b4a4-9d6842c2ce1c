#!/usr/bin/env bash
set -euo pipefail

# =============================================================================
# Docker deployment script to build and push to GitHub Container Registry
# =============================================================================

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# Ensure GHCR_TOKEN is set
if [ -z "${GHCR_TOKEN:-}" ]; then
  log_error "GHCR_TOKEN environment variable is not set."
  log_info "Create a GitHub PAT with write:packages scope and export it as GHCR_TOKEN."
  exit 1
fi

# Configuration - Choose your registry
REGISTRY="${REGISTRY:-dockerhub}"  # Options: dockerhub, ghcr
if [ "$REGISTRY" = "dockerhub" ]; then
  IMAGE="${IMAGE:-nxtleveltech1/nxtdotx:latest}"
  REGISTRY_URL="docker.io"
else
  IMAGE="${IMAGE:-ghcr.io/nxtleveltech1/nxtdotx:latest}"
  REGISTRY_URL="ghcr.io"
fi
USER="${USER:-nxtleveltech1}"
DOCKERFILE="${DOCKERFILE:-Dockerfile}"

# Validate Docker is running
if ! docker info >/dev/null 2>&1; then
  log_error "Docker is not running or not accessible"
  exit 1
fi

# Validate Dockerfile exists
if [ ! -f "$DOCKERFILE" ]; then
  log_error "Dockerfile not found: $DOCKERFILE"
  exit 1
fi

log_info "Starting deployment process..."
log_info "Image: $IMAGE"
log_info "User: $USER"
log_info "Dockerfile: $DOCKERFILE"

# Login to Container Registry
if [ "$REGISTRY" = "dockerhub" ]; then
  log_info "Logging in to Docker Hub as $USER"
  if [ -z "${DOCKER_PASSWORD:-}" ]; then
    log_error "DOCKER_PASSWORD environment variable is not set for Docker Hub"
    log_info "Set your Docker Hub password: export DOCKER_PASSWORD=your_password"
    exit 1
  fi
  if echo "$DOCKER_PASSWORD" | docker login -u "$USER" --password-stdin; then
    log_success "Successfully logged in to Docker Hub"
  else
    log_error "Failed to login to Docker Hub"
    exit 1
  fi
else
  log_info "Logging in to ghcr.io as $USER"
  if echo "$GHCR_TOKEN" | docker login ghcr.io -u "$USER" --password-stdin; then
    log_success "Successfully logged in to ghcr.io"
  else
    log_error "Failed to login to ghcr.io"
    exit 1
  fi
fi

# Build Docker image
log_info "Building Docker image $IMAGE"
if docker build -t "$IMAGE" -f "$DOCKERFILE" .; then
  log_success "Successfully built Docker image"
else
  log_error "Failed to build Docker image"
  exit 1
fi

# Push Docker image
log_info "Pushing Docker image $IMAGE"
if docker push "$IMAGE"; then
  log_success "Successfully pushed Docker image"
else
  log_error "Failed to push Docker image"
  exit 1
fi

# Test the image locally (optional)
log_info "Testing image locally..."
if docker run --rm --health-cmd="curl -f http://localhost:3000/health || exit 1" --health-interval=10s --health-timeout=5s --health-retries=3 -p 3000:3000 -d "$IMAGE"; then
  log_success "Image is running successfully"
  log_info "You can test it at http://localhost:3000/health"
else
  log_warning "Failed to test image locally (this is not critical)"
fi

log_success "Deployment complete: $IMAGE"
log_info "Image is now available at: $IMAGE"