#!/usr/bin/env node

/**
 * PHASE 2: MIGRATE EXISTING USER DATA TO BETTER AUTH
 * Migrates users from auth.users and public.profiles to Better Auth tables
 */

import postgres from 'postgres';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

console.log('🚀 PHASE 2: MIGRATING USER DATA TO BETTER AUTH');
console.log('===============================================');

// Validate environment variables
if (!process.env.DATABASE_URL) {
  console.error('❌ DATABASE_URL environment variable is required');
  process.exit(1);
}

// Create database connection
const sql = postgres(process.env.DATABASE_URL, {
  ssl: process.env.NODE_ENV === 'production' ? 'require' : false,
  max: 5,
  idle_timeout: 30,
  connect_timeout: 10,
});

async function migrateUserData() {
  try {
    console.log('🔗 Connecting to database...');
    
    // Test connection
    await sql`SELECT 1`;
    console.log('✅ Database connection successful');
    
    // Check existing data
    console.log('📊 Checking existing user data...');
    
    const existingAuthUsers = await sql`SELECT COUNT(*) as count FROM auth.users`;
    const existingProfiles = await sql`SELECT COUNT(*) as count FROM public.profiles`;
    const existingBetterAuthUsers = await sql`SELECT COUNT(*) as count FROM "user"`;
    
    console.log(`📈 auth.users: ${existingAuthUsers[0].count}`);
    console.log(`📈 public.profiles: ${existingProfiles[0].count}`);
    console.log(`📈 Better Auth users: ${existingBetterAuthUsers[0].count}`);
    
    if (existingAuthUsers[0].count === 0) {
      console.log('ℹ️  No users to migrate from auth.users');
      return;
    }
    
    console.log('🔄 Starting user data migration...');
    
    // Migrate users from auth.users to Better Auth user table
    console.log('👥 Migrating users from auth.users...');
    
    const migratedUsers = await sql`
      INSERT INTO "user" (id, email, "emailVerified", name, "createdAt", "updatedAt")
      SELECT 
        id::text,
        email,
        email_confirmed_at IS NOT NULL,
        COALESCE(
          raw_user_meta_data->>'name', 
          raw_user_meta_data->>'full_name', 
          split_part(email, '@', 1)
        ),
        created_at,
        updated_at
      FROM auth.users
      ON CONFLICT (email) DO UPDATE SET
        "emailVerified" = EXCLUDED."emailVerified",
        "updatedAt" = CURRENT_TIMESTAMP
      RETURNING id
    `;
    
    console.log(`✅ Migrated ${migratedUsers.length} users to Better Auth`);
    
    // Create password accounts for existing users
    console.log('🔐 Creating password accounts for migrated users...');
    
    const passwordAccounts = await sql`
      INSERT INTO "account" (id, "accountId", "providerId", "userId", password, "createdAt", "updatedAt")
      SELECT 
        'acc_' || au.id::text,
        au.id::text,
        'credential',
        au.id::text,
        au.encrypted_password,
        au.created_at,
        au.updated_at
      FROM auth.users au
      WHERE EXISTS (SELECT 1 FROM "user" u WHERE u.id = au.id::text)
      AND au.encrypted_password IS NOT NULL
      ON CONFLICT (id) DO NOTHING
      RETURNING id
    `;
    
    console.log(`✅ Created ${passwordAccounts.length} password accounts`);
    
    // Verify migration results
    console.log('🔍 Verifying migration results...');
    
    const finalBetterAuthUsers = await sql`SELECT COUNT(*) as count FROM "user"`;
    const finalAccounts = await sql`SELECT COUNT(*) as count FROM "account"`;
    
    console.log('📊 MIGRATION RESULTS:');
    console.log(`   Better Auth Users: ${finalBetterAuthUsers[0].count}`);
    console.log(`   Password Accounts: ${finalAccounts.length}`);
    
    // Test user lookup
    console.log('🧪 Testing user lookup...');
    
    const testUsers = await sql`
      SELECT 
        u.id,
        u.email,
        u.name,
        u."emailVerified",
        CASE WHEN a.id IS NOT NULL THEN true ELSE false END as has_password
      FROM "user" u
      LEFT JOIN "account" a ON u.id = a."userId" AND a."providerId" = 'credential'
      LIMIT 3
    `;
    
    console.log('👥 Sample migrated users:');
    testUsers.forEach(user => {
      console.log(`   📧 ${user.email} (${user.name}) - Password: ${user.has_password ? '✅' : '❌'}`);
    });
    
    console.log('');
    console.log('🎉 USER DATA MIGRATION COMPLETED SUCCESSFULLY!');
    console.log('');
    console.log('✅ Users migrated from auth.users to Better Auth');
    console.log('✅ Password accounts created for existing users');
    console.log('✅ Email verification status preserved');
    console.log('✅ User creation dates preserved');
    console.log('');
    console.log('🚀 Ready for Phase 3: Testing & Optimization!');
    
  } catch (error) {
    console.error('❌ User data migration failed:', error);
    console.error('🔍 Error details:', error.message);
    
    if (error.message.includes('duplicate key') || error.message.includes('already exists')) {
      console.log('');
      console.log('ℹ️  Some users may already be migrated. Checking current state...');
      
      try {
        const currentUsers = await sql`SELECT COUNT(*) as count FROM "user"`;
        const currentAccounts = await sql`SELECT COUNT(*) as count FROM "account"`;
        
        console.log('📊 Current Better Auth state:');
        console.log(`   Users: ${currentUsers[0].count}`);
        console.log(`   Accounts: ${currentAccounts[0].count}`);
        
        if (currentUsers[0].count > 0) {
          console.log('');
          console.log('✅ Users already migrated successfully!');
          console.log('🚀 Ready for Phase 3: Testing & Optimization!');
        }
      } catch (checkError) {
        console.error('❌ Failed to check migration state:', checkError.message);
        process.exit(1);
      }
    } else {
      process.exit(1);
    }
  } finally {
    await sql.end();
  }
}

// Execute migration
migrateUserData();
