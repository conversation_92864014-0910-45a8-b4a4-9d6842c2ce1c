# PRODUCTION ENVIRONMENT CONFIGURATION
# DO NOT COMMIT THIS FILE - USE SECRETS MANAGEMENT IN PRODUCTION

# =============================================================================
# CRITICAL: REPLACE ALL PLACEHOLDER VALUES WITH ACTUAL PRODUCTION SECRETS
# =============================================================================

# Database Configuration (PRODUCTION)
DATABASE_URL=postgresql://postgres.utxxvdztmbbjcwdkqxcc:<EMAIL>:6543/postgres

# Better Auth Configuration (PRODUCTION)
BETTER_AUTH_SECRET=0COok6fJ8f1HLwzPbwUbeoovkI9FK7ga
BETTER_AUTH_URL=https://nxtdotx.co.za
VITE_BETTER_AUTH_URL=https://nxtdotx.co.za

# Supabase Configuration (PRODUCTION)
VITE_SUPABASE_URL=https://utxxvdztmbbjcwdkqxcc.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV0eHh2ZHp0bWJiamN3ZGtxeGNjIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDkxNjYxNjMsImV4cCI6MjA2NDc0MjE2M30.O0vgYqezK88HfBU8_BTCljLXTj0ke2JUWWwBFzxtYN8
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV0eHh2ZHp0bWJiamN3ZGtxeGNjIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0OTE2NjE2MywiZXhwIjoyMDY0NzQyMTYzfQ.GpmQtjD_WeDvntcacN4heTiLHgPHfxxx7l7ULcW_nsA

# Server Configuration (PRODUCTION)
PORT=3000
HOST=0.0.0.0
NODE_ENV=production

# Application Configuration (PRODUCTION)
VITE_APP_NAME=NXT-DOT-X
VITE_APP_VERSION=1.0.0
VITE_APP_ENV=production
PRODUCTION_URL=https://nxtdotx.co.za

# API Configuration (PRODUCTION)
VITE_API_BASE_URL=https://utxxvdztmbbjcwdkqxcc.supabase.co/functions/v1
VITE_STORAGE_URL=https://utxxvdztmbbjcwdkqxcc.supabase.co/storage/v1

# Authentication (PRODUCTION)
VITE_AUTH_REDIRECT_URL=https://nxtdotx.co.za/auth/callback
VITE_AUTH_SITE_URL=https://nxtdotx.co.za

# Feature Flags (PRODUCTION)
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_REALTIME=true
VITE_ENABLE_STORAGE=true

# Security (PRODUCTION)
VITE_DEBUG_MODE=false
VITE_LOG_LEVEL=error

# File Upload Limits (PRODUCTION)
VITE_MAX_FILE_SIZE=10485760

# =============================================================================
# SECURITY NOTES:
# 1. Rotate all secrets regularly
# 2. Use environment-specific secrets management
# 3. Monitor for unauthorized access
# 4. Enable audit logging
# =============================================================================
