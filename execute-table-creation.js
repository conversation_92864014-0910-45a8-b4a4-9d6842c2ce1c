#!/usr/bin/env node

/**
 * PHASE 2: CREATE BETTER AUTH TABLES
 * Executes the table creation script
 */

import postgres from 'postgres';
import fs from 'fs';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

console.log('🚀 PHASE 2: CREATING BETTER AUTH TABLES');
console.log('========================================');

// Validate environment variables
if (!process.env.DATABASE_URL) {
  console.error('❌ DATABASE_URL environment variable is required');
  process.exit(1);
}

// Create database connection
const sql = postgres(process.env.DATABASE_URL, {
  ssl: process.env.NODE_ENV === 'production' ? 'require' : false,
  max: 5,
  idle_timeout: 30,
  connect_timeout: 10,
});

async function createTables() {
  try {
    console.log('🔗 Connecting to database...');
    
    // Test connection
    await sql`SELECT 1`;
    console.log('✅ Database connection successful');
    
    // Read table creation script
    console.log('📖 Reading table creation script...');
    const createTablesSQL = fs.readFileSync('create-better-auth-tables.sql', 'utf8');
    
    // Execute table creation
    console.log('🔄 Creating Better Auth tables...');
    
    await sql.unsafe(createTablesSQL);
    
    console.log('✅ Better Auth tables created successfully!');
    
    // Verify tables exist
    console.log('🔍 Verifying table creation...');
    
    const tables = await sql`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name IN ('user', 'session', 'account', 'verification')
      ORDER BY table_name
    `;

    console.log('📋 Better Auth tables verified:');
    tables.forEach(table => {
      console.log(`   ✅ ${table.table_name}`);
    });

    if (tables.length === 4) {
      console.log('');
      console.log('🎉 ALL BETTER AUTH TABLES CREATED SUCCESSFULLY!');
      console.log('');
      console.log('✅ user table - stores user accounts');
      console.log('✅ session table - stores user sessions');
      console.log('✅ account table - stores auth provider accounts');
      console.log('✅ verification table - stores verification tokens');
      console.log('');
      console.log('🚀 Ready for user data migration!');
    } else {
      console.log('⚠️  Some tables may not have been created properly');
    }
    
  } catch (error) {
    console.error('❌ Table creation failed:', error);
    console.error('🔍 Error details:', error.message);
    
    if (error.message.includes('already exists')) {
      console.log('');
      console.log('ℹ️  Tables may already exist. This is OK.');
      console.log('🔍 Checking existing tables...');
      
      try {
        const existingTables = await sql`
          SELECT table_name 
          FROM information_schema.tables 
          WHERE table_schema = 'public' 
          AND table_name IN ('user', 'session', 'account', 'verification')
          ORDER BY table_name
        `;
        
        console.log('📋 Existing Better Auth tables:');
        existingTables.forEach(table => {
          console.log(`   ✅ ${table.table_name}`);
        });
        
        if (existingTables.length === 4) {
          console.log('');
          console.log('🎉 ALL BETTER AUTH TABLES ALREADY EXIST!');
          console.log('🚀 Ready for user data migration!');
        }
      } catch (checkError) {
        console.error('❌ Failed to check existing tables:', checkError.message);
        process.exit(1);
      }
    } else {
      process.exit(1);
    }
  } finally {
    await sql.end();
  }
}

// Execute table creation
createTables();
