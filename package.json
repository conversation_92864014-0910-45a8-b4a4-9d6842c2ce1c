{"name": "nxt-web-dev-0x", "private": true, "version": "3.5.1", "description": "Comprehensive Business Management Platform", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "preview": "vite preview", "type-check": "tsc --noEmit", "clean": "<PERSON><PERSON><PERSON> dist", "build:prod": "npm run clean && npm run type-check && npm run lint && npm run build", "test": "jest", "test:watch": "jest --watch", "db:setup": "echo 'Database setup - configure your Supabase connection'", "migrate": "echo 'Run database migrations'", "deploy": "npm run build:prod && echo 'Ready for deployment'", "backup": "node scripts/backup-utility.js create", "backup:quick": "node scripts/backup-utility.js quick", "backup:list": "node scripts/backup-utility.js list"}, "dependencies": {"@daveyplate/better-auth-ui": "^2.0.5", "@hookform/resolvers": "^5.1.1", "@marsidev/react-turnstile": "^1.1.0", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.0", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-tooltip": "^1.1.3", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.50.0", "@tanstack/react-query": "^5.62.7", "@tanstack/react-table": "^8.20.5", "better-auth": "^1.2.9", "buffer": "^6.0.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.4", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dart-tools": "^0.3.5", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "drizzle-orm": "^0.44.2", "express": "^5.1.0", "framer-motion": "^11.15.0", "input-otp": "^1.4.1", "lucide-react": "^0.515.0", "next-themes": "^0.4.6", "path-to-regexp": "^6.2.1", "pg": "^8.16.0", "postgres": "^3.4.7", "postgres-js": "^0.1.0", "react": "^18.3.1", "react-day-picker": "^9.7.0", "react-dom": "^18.3.1", "react-dropzone": "^14.3.8", "react-hook-form": "^7.54.2", "react-router-dom": "^6.28.0", "recharts": "^2.13.3", "sonner": "^1.7.1", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "xlsx": "^0.18.5", "zod": "^3.24.1", "zustand": "^5.0.2"}, "devDependencies": {"@babel/preset-env": "^7.27.2", "@babel/preset-typescript": "^7.27.1", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/cookie-parser": "^1.4.7", "@types/cors": "^2.8.17", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/node": "^22.10.2", "@types/pg": "^8.11.10", "@types/react": "^18.3.17", "@types/react-dom": "^18.3.5", "@typescript-eslint/eslint-plugin": "^8.18.1", "@typescript-eslint/parser": "^8.18.1", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "concurrently": "^9.1.2", "eslint": "^9.17.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8.5.1", "rimraf": "^6.0.1", "tailwindcss": "^3.4.17", "ts-jest": "^29.3.4", "ts-jest-mock-import-meta": "^1.3.0", "typescript": "^5.8.3", "vite": "^6.3.5", "vitest": "^3.2.3"}, "overrides": {"glob": "^11.0.0", "inflight": "^1.0.6", "rimraf": "^6.0.1"}, "comments": {"xlsx": "Note: xlsx package has known security vulnerabilities but is required for Excel file processing. Consider input validation and sandboxing for production use."}}