import { betterAuth } from "better-auth";
import { drizzleAdapter } from "better-auth/adapters/drizzle";
import cors from "cors";
import dotenv from "dotenv";
import { drizzle } from "drizzle-orm/postgres-js";
import express from "express";
import path from "path";
import postgres from "postgres";

// Simple rate limiting implementation
const rateLimitMap = new Map();
const RATE_LIMIT_WINDOW = 15 * 60 * 1000; // 15 minutes
const RATE_LIMIT_MAX_REQUESTS = 100; // requests per window

function rateLimit(req, res, next) {
  const clientIP = req.ip || req.connection.remoteAddress;
  const now = Date.now();

  if (!rateLimitMap.has(clientIP)) {
    rateLimitMap.set(clientIP, { count: 1, resetTime: now + RATE_LIMIT_WINDOW });
    return next();
  }

  const clientData = rateLimitMap.get(clientIP);

  if (now > clientData.resetTime) {
    clientData.count = 1;
    clientData.resetTime = now + RATE_LIMIT_WINDOW;
    return next();
  }

  if (clientData.count >= RATE_LIMIT_MAX_REQUESTS) {
    return res.status(429).json({
      error: 'Too many requests',
      message: 'Rate limit exceeded. Please try again later.',
      retryAfter: Math.ceil((clientData.resetTime - now) / 1000)
    });
  }

  clientData.count++;
  next();
}
import { schema } from "../src/lib/better-auth-schema.js";

// Load environment variables
dotenv.config();

console.log('🚀 Starting FIXED Production Better-Auth Server...');
console.log('🌐 Environment:', process.env.NODE_ENV || 'production');

const PORT = process.env.PORT || 3000;

// Validate required environment variables
const requiredEnvVars = [
  'DATABASE_URL',
  'BETTER_AUTH_SECRET'
];

const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
if (missingVars.length > 0) {
  console.error('❌ Missing required environment variables:', missingVars.join(', '));
  process.exit(1);
}

console.log('✅ All required environment variables are set');
console.log('🔗 Connecting to production database...');

// Create database connection using postgres.js (more reliable than pg)
const client = postgres(process.env.DATABASE_URL, {
  ssl: process.env.NODE_ENV === 'production' ? 'require' : false,
  max: 20,
  idle_timeout: 30,
  connect_timeout: 10,
});

const db = drizzle(client);

// Test database connection
async function testConnection() {
  try {
    const result = await client`SELECT NOW() as current_time, version() as pg_version`;
    console.log('✅ Database connected successfully');
    console.log('📅 Server time:', result[0].current_time);
    console.log('🗄️  PostgreSQL version:', result[0].pg_version.split(' ')[0]);
    return true;
  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
    return false;
  }
}

// Initialize Better Auth with Drizzle adapter (more stable)
let auth;
async function initializeBetterAuth() {
  try {
    console.log('🔧 Initializing Better Auth with Drizzle adapter...');

    // Test database connection first
    await client`SELECT 1`;
    console.log('✅ Database connection verified for Better Auth');

    auth = betterAuth({
      database: drizzleAdapter(db, {
        provider: "pg",
        schema: schema,
      }),
      emailAndPassword: {
        enabled: true,
        requireEmailVerification: false,
      },
      session: {
        expiresIn: 60 * 60 * 24 * 7, // 7 days
        updateAge: 60 * 60 * 24, // 24 hours
      },
      secret: process.env.BETTER_AUTH_SECRET,
      baseURL: 'https://nxtdotx.co.za',
      trustedOrigins: [
        'https://nxtdotx.co.za',
        'https://www.nxtdotx.co.za'
      ]
    });

    console.log('✅ Better Auth initialized successfully');
    return true;
  } catch (error) {
    console.error('❌ Better Auth initialization failed:', error);
    console.error('🔍 Error details:', error.message);
    return false;
  }
}

// Create Express app
const app = express();

// Apply rate limiting
app.use(rateLimit);

// CORS configuration
app.use(cors({
  origin: [
    'https://nxtdotx.co.za',
    'https://www.nxtdotx.co.za',
    'http://localhost:3000',
    'http://localhost:5173'
  ],
  credentials: true
}));

// Security middleware
app.use((req, res, next) => {
  // Security headers
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('X-XSS-Protection', '1; mode=block');
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
  res.setHeader('Permissions-Policy', 'geolocation=(), microphone=(), camera=()');

  // HTTPS redirect in production
  if (process.env.NODE_ENV === 'production' && req.header('x-forwarded-proto') !== 'https') {
    return res.redirect(`https://${req.header('host')}${req.url}`);
  }

  next();
});

app.use(express.json({ limit: '10mb' }));
app.use(express.static('dist'));

// Health check endpoint
app.get("/health", (req, res) => {
  res.status(200).json({
    status: "healthy",
    timestamp: new Date().toISOString(),
    auth: auth ? "initialized" : "not initialized",
    database: "connected"
  });
});

// Mount Better Auth endpoints with proper URL handling
app.all("/api/auth/*", async (req, res) => {
  try {
    if (!auth) {
      console.error("❌ Auth not initialized");
      return res.status(503).json({
        error: "Service unavailable",
        details: "Better Auth not initialized"
      });
    }

    console.log(`📡 ${req.method} ${req.url}`);

    // Fix the URL for Better Auth - it expects the full URL
    const protocol = req.headers['x-forwarded-proto'] || 'https';
    const host = req.headers.host || 'nxtdotx.co.za';
    const fullUrl = `${protocol}://${host}${req.url}`;

    console.log(`🔗 Full URL: ${fullUrl}`);

    // Create a proper request object with the full URL
    const modifiedReq = {
      ...req,
      url: fullUrl
    };

    // Try to handle the request
    const result = await auth.handler(modifiedReq, res);
    return result;

  } catch (error) {
    console.error("❌ Auth handler error:", error.message);
    console.error("🔍 Stack:", error.stack);

    // Return detailed error for debugging
    return res.status(500).json({
      error: "Internal server error",
      details: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
});

// Serve frontend for all other routes
app.get('*', (req, res) => {
  res.sendFile(path.join(process.cwd(), 'dist/index.html'));
});

// Production logging middleware
app.use((req, res, next) => {
  const start = Date.now();
  const originalSend = res.send;

  res.send = function(data) {
    const duration = Date.now() - start;
    console.log(`${new Date().toISOString()} ${req.method} ${req.url} ${res.statusCode} ${duration}ms`);
    originalSend.call(this, data);
  };

  next();
});

// Global error handlers
process.on('uncaughtException', (error) => {
  console.error(`${new Date().toISOString()} ❌ Uncaught Exception:`, error.message);
  console.error('🔍 Stack:', error.stack);
  // In production, you might want to restart the process
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error(`${new Date().toISOString()} ❌ Unhandled Rejection at:`, promise, 'reason:', reason);
  // In production, you might want to restart the process
  process.exit(1);
});

// Start server after successful database connection and auth initialization
testConnection().then(async (dbSuccess) => {
  if (dbSuccess) {
    const authSuccess = await initializeBetterAuth();
    if (authSuccess) {
      const server = app.listen(PORT, () => {
        console.log(`✅ FIXED Better Auth Server running on port ${PORT}`);
        console.log(`🌐 Frontend: https://nxtdotx.co.za`);
        console.log(`🔐 Auth API: https://nxtdotx.co.za/api/auth`);
        console.log(`🔍 Health: https://nxtdotx.co.za/health`);
      });
      
      server.on('error', (error) => {
        console.error('❌ Server error:', error);
      });
    } else {
      console.error('❌ Server failed to start due to Better Auth initialization issues');
      process.exit(1);
    }
  } else {
    console.error('❌ Server failed to start due to database connection issues');
    process.exit(1);
  }
}).catch(error => {
  console.error('❌ Startup error:', error);
  process.exit(1);
});
