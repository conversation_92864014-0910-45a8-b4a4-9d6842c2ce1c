import { betterAuth } from "better-auth";
import cors from "cors";
import dotenv from "dotenv";
import express from "express";
import { Pool } from "pg";

// Make process available in ESM
/* global process */

// Load environment variables
dotenv.config();

console.log('🚀 Starting Production Better-Auth Server...');
console.log('🌐 Environment:', process.env.NODE_ENV || 'production');
console.log('🔗 Connecting to production database...');

// Create PostgreSQL connection pool for Supabase
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: {
    rejectUnauthorized: false
  },
  max: 20,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
});

// Test database connection
async function testConnection() {
  try {
    const client = await pool.connect();
    const result = await client.query('SELECT NOW() as current_time, version() as pg_version');
    console.log('✅ Database connected successfully');
    console.log('📅 Server time:', result.rows[0].current_time);
    console.log('🗄️  PostgreSQL version:', result.rows[0].pg_version.split(' ')[0]);
    client.release();
    return true;
  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
    return false;
  }
}

// Initialize Better Auth with PostgreSQL connection
const auth = betterAuth({
  database: {
    provider: "postgresql",
    connection: pool,
  },
  emailAndPassword: {
    enabled: true,
    requireEmailVerification: false, // Set to true in production if needed
  },
  session: {
    expiresIn: 60 * 60 * 24 * 7, // 7 days
    updateAge: 60 * 60 * 24, // 24 hours
  },
  trustedOrigins: [
    'https://nxtdotx.co.za',
    'https://www.nxtdotx.co.za',
    process.env.PRODUCTION_URL || 'https://nxtdotx.co.za'
  ]
});

const app = express();
app.use(cors({
  origin: [
    'https://nxtdotx.co.za',
    'https://www.nxtdotx.co.za',
    process.env.PRODUCTION_URL || 'https://nxtdotx.co.za'
  ],
  credentials: true
}));
app.use(express.json());

// Health check endpoint
app.get("/health", (req, res) => {
  res.status(200).json({
    status: "healthy",
    timestamp: new Date().toISOString(),
    service: "better-auth-server"
  });
});

// Mount Better Auth endpoints using Express Router approach
app.use("/api/auth", async (req, res) => {
  try {
    console.log(`📡 ${req.method} ${req.url}`);
    return await auth.handler(req, res);
  } catch (error) {
    console.error("❌ Auth handler error:", error);
    res.status(500).json({
      error: "Internal server error",
      details: error.message
    });
  }
});

const PORT = process.env.PORT || process.env.AUTH_PORT || 3001;

// Start server after successful database connection
testConnection().then(success => {
  if (success) {
    app.listen(PORT, () => {
      console.log(`✅ Better Auth Server running on port ${PORT}`);
      console.log(`🌐 Endpoint: https://nxtdotx.co.za/api/auth`);
    });
  } else {
    console.error('❌ Server failed to start due to database connection issues');
    process.exit(1);
  }
});